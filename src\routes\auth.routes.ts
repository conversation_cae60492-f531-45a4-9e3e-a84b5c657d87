import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as authController from '../controllers/auth.controller';

export async function authRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.post('/register', authController.register as any);
  fastify.post('/login', authController.login as any);
  fastify.post('/refresh', authController.refreshToken as any);
  fastify.post('/logout', { preHandler: AuthMiddleware.authenticateRequest }, authController.logout as any);
  fastify.post('/2fa/setup', { preHandler: AuthMiddleware.authenticateRequest }, authController.setup2FA as any);
  fastify.post('/2fa/verify', { preHandler: AuthMiddleware.authenticateRequest }, authController.verify2FA as any);
  fastify.delete('/2fa', { preHandler: AuthMiddleware.authenticateRequest }, authController.disable2FA as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'auth service running',
      data: { status: 'ok' }
    };
  });
}
