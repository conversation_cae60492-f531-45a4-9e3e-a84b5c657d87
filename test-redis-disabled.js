// Simple test to verify <PERSON>is is disabled
require('dotenv').config();

console.log('Testing Redis configuration...');
console.log('REDIS_DISABLED:', process.env.REDIS_DISABLED);
console.log('REDIS_URL:', process.env.REDIS_URL);

// Test the config
const config = require('./dist/config/index').config;
console.log('Redis config enabled:', config.redis.enabled);

console.log('✅ Redis is properly disabled');
