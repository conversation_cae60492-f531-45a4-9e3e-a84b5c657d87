import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as loanController from '../controllers/loan.controller';

export async function loanRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.post('/apply', { preHandler: AuthMiddleware.authenticateRequest }, loanController.applyForLoan as any);
  fastify.get('/my-loans', { preHandler: AuthMiddleware.authenticateRequest }, loanController.getUserLoans as any);
  fastify.get('/offers', { preHandler: AuthMiddleware.authenticateRequest }, loanController.getLoanOffers as any);
  fastify.get('/:loanId', { preHandler: AuthMiddleware.authenticateRequest }, loanController.getLoanDetails as any);
  
  fastify.post('/:loanId/repay', { preHandler: AuthMiddleware.authenticateRequest }, loanController.makeRepayment as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'loan service running',
      data: { 
        status: 'ok',
        services: ['loan_application', 'loan_management', 'repayment', 'offers']
      }
    };
  });
}
