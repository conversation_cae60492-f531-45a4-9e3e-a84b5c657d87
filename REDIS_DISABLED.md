# Redis Disabled Configuration

<PERSON><PERSON> has been disabled in this project to simplify development and deployment.

## What was changed:

### 1. Server startup (`src/server.ts`)
- Removed Redis connection attempt
- Removed Redis import
- Updated graceful shutdown to skip Redis disconnect

### 2. Redis configuration (`src/config/redis.ts`)
- Added check for `REDIS_DISABLED` environment variable
- Skip connection if <PERSON><PERSON> is disabled or no URL provided

### 3. Configuration (`src/config/index.ts`)
- Added `enabled` flag to Redis config
- Redis is disabled by default

### 4. Environment variables
- Added `REDIS_DISABLED=true` to `.env` and `.env.example`
- Redis will not attempt to connect when this flag is set

### 5. Health checks (`src/routes/health.routes.ts`)
- Redis health endpoint now returns "disabled" status instead of trying to connect

## Fallback behavior:

The application already has proper fallback mechanisms for when Redis is unavailable:

- **Rate limiting**: Falls back to in-memory rate limiting
- **Token blacklisting**: Gracefully handles Redis errors (returns false)
- **Caching**: All cache operations return false/null when <PERSON><PERSON> is unavailable

## To re-enable Redis:

1. Set `REDIS_DISABLED=false` in your `.env` file
2. Provide a valid `REDIS_URL`
3. Restart the application

## Benefits of disabling Redis:

- ✅ Simpler development setup
- ✅ Fewer dependencies to manage
- ✅ Faster startup time
- ✅ No Redis installation required
- ✅ Application still functions with fallback mechanisms
