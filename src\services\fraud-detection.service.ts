import { FraudAlert } from '../models/fraud-alert.model';
import { Transaction } from '../models/transaction.model';
import { User } from '../models/user.model';
import { SystemConfig } from '../models/system-config.model';
import { AuditLog } from '../models/audit-log.model';
import { logger } from '../config/logger';
import { AuditAction } from '../types';
import mongoose from 'mongoose';

export class FraudDetectionService {
  static async analyzeTransaction(transactionData: {
    userId: string;
    amount: number;
    currency: string;
    type: string;
    ipAddress: string;
    userAgent: string;
    location?: { country: string; city: string; };
  }): Promise<{ riskScore: number; alerts: any[] }> {
    try {
      const alerts: any[] = [];
      let riskScore = 0;

      // get fraud detection thresholds from config
      const velocityThreshold = await SystemConfig.getConfig('fraud_velocity_threshold') || 5;
      const amountThreshold = await SystemConfig.getConfig('fraud_amount_threshold') || 10000;
      const locationCheckEnabled = await SystemConfig.getConfig('fraud_location_check_enabled') || true;

      // check transaction velocity (number of transactions in last hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const recentTransactions = await Transaction.countDocuments({
        user_id: transactionData.userId,
        created_at: { $gte: oneHourAgo }
      });

      if (recentTransactions >= velocityThreshold) {
        riskScore += 30;
        alerts.push({
          type: 'velocity_check',
          severity: recentTransactions >= velocityThreshold * 2 ? 'high' : 'medium',
          description: `${recentTransactions} transactions in the last hour`,
          details: { transaction_count: recentTransactions, threshold: velocityThreshold }
        });
      }

      // check large amount
      if (transactionData.amount >= amountThreshold) {
        riskScore += 25;
        alerts.push({
          type: 'suspicious_transaction',
          severity: transactionData.amount >= amountThreshold * 2 ? 'high' : 'medium',
          description: `Large transaction amount: ${transactionData.amount} ${transactionData.currency}`,
          details: { amount: transactionData.amount, threshold: amountThreshold }
        });
      }

      // check unusual location
      if (locationCheckEnabled && transactionData.location) {
        const userLocationHistory = await this.getUserLocationHistory(transactionData.userId);
        if (!userLocationHistory.includes(transactionData.location.country)) {
          riskScore += 20;
          alerts.push({
            type: 'location_anomaly',
            severity: 'medium',
            description: `Transaction from unusual location: ${transactionData.location.country}`,
            details: { 
              current_location: transactionData.location,
              previous_locations: userLocationHistory
            }
          });
        }
      }

      // check device/IP patterns
      const ipRiskScore = await this.analyzeIpAddress(transactionData.ipAddress, transactionData.userId);
      riskScore += ipRiskScore;

      if (ipRiskScore > 15) {
        alerts.push({
          type: 'device_anomaly',
          severity: ipRiskScore > 30 ? 'high' : 'medium',
          description: 'Suspicious IP address or device pattern',
          details: { ip_address: transactionData.ipAddress, risk_score: ipRiskScore }
        });
      }

      // create fraud alerts if risk score is high
      if (riskScore >= 50) {
        for (const alert of alerts) {
          await FraudAlert.createAlert({
            alertType: alert.type,
            severity: alert.severity,
            userId: transactionData.userId,
            riskScore,
            description: alert.description,
            details: alert.details
          });
        }
      }

      return { riskScore, alerts };
    } catch (error: any) {
      logger.error('fraud analysis error:', error);
      return { riskScore: 0, alerts: [] };
    }
  }

  static async analyzeLoginPattern(loginData: {
    userId: string;
    ipAddress: string;
    userAgent: string;
    location?: { country: string; city: string; };
    timestamp: Date;
  }): Promise<{ riskScore: number; shouldBlock: boolean }> {
    try {
      let riskScore = 0;

      // check for multiple failed login attempts
      const failedLoginThreshold = await SystemConfig.getConfig('failed_login_threshold') || 5;
      const failedLoginWindow = await SystemConfig.getConfig('failed_login_window_minutes') || 30;

      const windowStart = new Date(Date.now() - failedLoginWindow * 60 * 1000);
      const failedAttempts = await AuditLog.countDocuments({
        user_id: loginData.userId,
        action: AuditAction.USER_LOGIN,
        status: 'failed',
        created_at: { $gte: windowStart }
      });

      if (failedAttempts >= failedLoginThreshold) {
        riskScore += 40;
      } else if (failedAttempts >= Math.floor(failedLoginThreshold / 2)) {
        riskScore += 20;
      }
      
      // check login location
      const userLocationHistory = await this.getUserLocationHistory(loginData.userId);
      if (loginData.location && !userLocationHistory.includes(loginData.location.country)) {
        riskScore += 25;
        
        await FraudAlert.createAlert({
          alertType: 'unusual_login',
          severity: 'medium',
          userId: loginData.userId,
          riskScore,
          description: `Login from unusual location: ${loginData.location.country}`,
          details: {
            login_location: loginData.location,
            previous_locations: userLocationHistory,
            ip_address: loginData.ipAddress
          }
        });
      }

      // check IP reputation
      const ipRiskScore = await this.analyzeIpAddress(loginData.ipAddress, loginData.userId);
      riskScore += ipRiskScore;

      const shouldBlock = riskScore >= 70;

      return { riskScore, shouldBlock };
    } catch (error: any) {
      logger.error('login pattern analysis error:', error);
      return { riskScore: 0, shouldBlock: false };
    }
  }

  private static async getUserLocationHistory(userId: string): Promise<string[]> {
    try {
      // get user's transaction history to determine usual locations
      const transactions = await Transaction.aggregate([
        { $match: { user_id: new mongoose.Types.ObjectId(userId) } },
        { $group: { _id: '$metadata.location.country' } },
        { $match: { _id: { $ne: null } } }
      ]);

      return transactions.map(t => t._id).filter(Boolean);
    } catch (error) {
      return [];
    }
  }

  private static async analyzeIpAddress(ipAddress: string, userId: string): Promise<number> {
    try {
      let riskScore = 0;

      // check if IP has been used by multiple users recently
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const ipUsageCount = await Transaction.distinct('user_id', {
        'metadata.ip_address': ipAddress,
        created_at: { $gte: oneWeekAgo }
      });

      if (ipUsageCount.length > 5) {
        riskScore += 20;
      }

      // check if user is using multiple IPs in short time
      const userIpCount = await Transaction.distinct('metadata.ip_address', {
        user_id: new mongoose.Types.ObjectId(userId),
        created_at: { $gte: oneWeekAgo }
      });

      if (userIpCount.length > 10) {
        riskScore += 15;
      }

      return riskScore;
    } catch (error) {
      return 0;
    }
  }

  static async getActiveAlerts(filters?: {
    severity?: string;
    status?: string;
    assignedTo?: string;
    page?: number;
    limit?: number;
  }): Promise<any> {
    try {
      return await FraudAlert.getAlerts(filters || {});
    } catch (error: any) {
      logger.error('get active alerts error:', error);
      throw error;
    }
  }

  static async assignAlert(alertId: string, assignedTo: string): Promise<any> {
    try {
      return await FraudAlert.assignAlert(alertId, assignedTo);
    } catch (error: any) {
      logger.error('assign alert error:', error);
      throw error;
    }
  }

  static async resolveAlert(alertId: string, resolutionNotes: string, resolvedBy: string): Promise<any> {
    try {
      return await FraudAlert.resolveAlert(alertId, resolutionNotes, resolvedBy);
    } catch (error: any) {
      logger.error('resolve alert error:', error);
      throw error;
    }
  }

  static async blockUser(userId: string, reason: string, blockedBy: string): Promise<boolean> {
    try {
      await User.findByIdAndUpdate(userId, {
        account_status: 'suspended',
        suspension_reason: reason,
        suspended_by: new mongoose.Types.ObjectId(blockedBy),
        suspended_at: new Date()
      });

      logger.warn('user blocked due to fraud', {
        userId,
        reason,
        blockedBy
      });

      return true;
    } catch (error: any) {
      logger.error('block user error:', error);
      return false;
    }
  }

  static async generateRiskReport(dateFrom: Date, dateTo: Date): Promise<any> {
    try {
      const [
        totalAlerts,
        alertsBySeverity,
        alertsByType,
        resolvedAlerts,
        topRiskyUsers
      ] = await Promise.all([
        FraudAlert.countDocuments({
          created_at: { $gte: dateFrom, $lte: dateTo }
        }),
        FraudAlert.aggregate([
          { $match: { created_at: { $gte: dateFrom, $lte: dateTo } } },
          { $group: { _id: '$severity', count: { $sum: 1 } } }
        ]),
        FraudAlert.aggregate([
          { $match: { created_at: { $gte: dateFrom, $lte: dateTo } } },
          { $group: { _id: '$alert_type', count: { $sum: 1 } } }
        ]),
        FraudAlert.countDocuments({
          created_at: { $gte: dateFrom, $lte: dateTo },
          status: 'resolved'
        }),
        FraudAlert.aggregate([
          { $match: { created_at: { $gte: dateFrom, $lte: dateTo } } },
          { $group: { 
            _id: '$user_id', 
            alert_count: { $sum: 1 },
            avg_risk_score: { $avg: '$risk_score' }
          }},
          { $sort: { alert_count: -1 } },
          { $limit: 10 },
          { $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'user'
          }}
        ])
      ]);

      return {
        summary: {
          total_alerts: totalAlerts,
          resolved_alerts: resolvedAlerts,
          resolution_rate: totalAlerts > 0 ? (resolvedAlerts / totalAlerts * 100).toFixed(2) : 0
        },
        alerts_by_severity: alertsBySeverity,
        alerts_by_type: alertsByType,
        top_risky_users: topRiskyUsers,
        generated_at: new Date().toISOString()
      };
    } catch (error: any) {
      logger.error('generate risk report error:', error);
      throw error;
    }
  }
}
